package billing

import (
	"errors"
	"net/http"

	"git.uozi.org/uozi/potato-billing-api/api"
	"git.uozi.org/uozi/potato-billing-api/internal/app"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
)

// 这些类型定义已经移动到 internal/app 模块中

// RegisterAppAPI 初始化应用管理接口
func RegisterAppAPI(g *gin.RouterGroup) {
	g.GET("/apps", GetAppList)
	g.GET("/apps/:app_id", GetApp)
	g.PUT("/apps/:app_id", UpdateApp)
	g.DELETE("/apps/:app_id", DeleteApp)
	g.POST("/apps", CreateApp)
	g.POST("/apps/:app_id", UpdateApp)
	g.GET("/apps/overview/stats", GetAppOverviewStats)
	g.GET("/apps/:app_id/status", GetAppStatus)
}

// GetAppList 获取应用列表
func GetAppList(c *gin.Context) {
	// 解析查询参数
	var query app.AppListQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 调用 internal 模块获取应用列表
	response, err := app.GetAppList(c.Request.Context(), &query)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, response)
}

// CreateApp 创建应用
func CreateApp(c *gin.Context) {
	user := api.CurrentUser(c)

	var req app.CreateAppRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 调用 internal 模块创建应用
	result, err := app.CreateApp(c.Request.Context(), user.ID, &req)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, result)
}

// UpdateAppRequest 类型定义已移动到 internal/app 模块中

// GetApp 获取应用
func GetApp(c *gin.Context) {
	appID := cast.ToUint64(c.Param("app_id"))
	if appID == 0 {
		cosy.ErrHandler(c, errors.New("应用ID参数必需"))
		return
	}

	// 调用 internal 模块获取应用
	result, err := app.GetApp(c.Request.Context(), appID, 0) // userID 为 0 表示管理员查询
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, result)
}

// UpdateApp 更新应用
func UpdateApp(c *gin.Context) {
	appID := cast.ToUint64(c.Param("app_id"))
	if appID == 0 {
		cosy.ErrHandler(c, errors.New("应用ID参数必需"))
		return
	}

	var req app.UpdateAppRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 调用 internal 模块更新应用
	result, err := app.UpdateApp(c.Request.Context(), appID, 0, &req) // userID 为 0 表示管理员操作
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, result)
}

// DeleteApp 删除应用
func DeleteApp(c *gin.Context) {
	appID := cast.ToUint64(c.Param("app_id"))
	if appID == 0 {
		cosy.ErrHandler(c, errors.New("应用ID参数必需"))
		return
	}

	// 调用 internal 模块删除应用
	err := app.DeleteApp(c.Request.Context(), appID, 0) // userID 为 0 表示管理员操作
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "应用删除成功"})
}

// GetAppStatus 获取应用状态
func GetAppStatus(c *gin.Context) {
	appID := cast.ToUint64(c.Param("app_id"))
	if appID == 0 {
		cosy.ErrHandler(c, errors.New("应用ID参数必需"))
		return
	}

	// 调用 internal 模块获取应用状态
	response, err := app.GetAppStatus(c.Request.Context(), appID)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetAppOverviewStats 获取应用概览统计
func GetAppOverviewStats(c *gin.Context) {
	// 调用 internal 模块获取应用概览统计
	stats, err := app.GetAppOverviewStats(c.Request.Context())
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, stats)
}
