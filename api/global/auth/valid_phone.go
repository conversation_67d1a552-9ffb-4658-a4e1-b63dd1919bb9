package auth

import (
	"fmt"
	"net/http"
	"time"

	"git.uozi.org/uozi/potato-billing-api/internal/user"
	"git.uozi.org/uozi/potato-billing-api/pkg/sms"
	"git.uozi.org/uozi/potato-billing-api/pkg/valid"
	"git.uozi.org/uozi/potato-billing-api/settings"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

func ValidPhone(c *gin.Context) {
	var data struct {
		AreaCode int    `json:"area_code"  binding:"required"`
		Phone    string `json:"phone" binding:"required"`
	}

	// check if the captcha not expired
	var keyExist interface{}
	if !cosy.BindAndValid(c, &data) {
		return
	}
	key := fmt.Sprintf("%d%s", data.AreaCode, data.Phone)
	verifyKey := fmt.Sprintf(valid.VerifyKey, key)
	throttleKey := fmt.Sprintf(valid.ThrottleKey, key)
	if keyExist = valid.CheckExist(throttleKey); keyExist != nil {
		c.JSON(http.StatusTooManyRequests, gin.H{
			"code":    http.StatusTooManyRequests,
			"message": "too many requests",
		})
		return
	}
	// generate verification code
	code, err := user.GenerateVerificationCode()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}
	valid.SetWithTTL(throttleKey, code, time.Duration(60)*time.Second)
	valid.SetWithTTL(verifyKey, code, time.Duration(120)*time.Second)

	// Dry run mode
	if settings.SMSSettings.DryRun {
		c.JSON(http.StatusOK, gin.H{
			"captcha": code,
			"message": "verification code sent successfully",
		})
		return
	}

	templateCode := settings.SMSSettings.CNTemplateCode
	err = sms.Mail(key, templateCode, fmt.Sprintf("{'code':'%d'}", code))
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"message": "verification code sent successfully",
	})
}
