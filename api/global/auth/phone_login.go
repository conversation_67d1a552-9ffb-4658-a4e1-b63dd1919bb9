package auth

import (
	"context"
	"encoding/base64"
	"net/http"

	"git.uozi.org/uozi/crypto"
	"git.uozi.org/uozi/potato-billing-api/api"
	"git.uozi.org/uozi/potato-billing-api/internal/user"
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/pkg/valid"
	"git.uozi.org/uozi/potato-billing-api/settings"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
)

func PhoneLogin(c *gin.Context) {
	var json struct {
		AreaCode int    `json:"area_code"`
		Phone    string `json:"phone" binding:"min=8,max=12"`
		Captcha  string `json:"captcha" binding:"required,len=6"`
	}

	if !cosy.BindAndValid(c, &json) {
		return
	}

	if !settings.SMSSettings.IgnoreCodeCheck &&
		!valid.CheckCode(json.AreaCode, json.Phone, cast.ToString(json.Captcha)) {
		cosy.ErrHandler(c, user.ErrInvalidCode)
		return
	}

	aesEncrypted, err := crypto.AesEncrypt([]byte(json.Phone))
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	db := cosy.UseDB(context.Background())

	name := "土豆子 " + json.Phone[len(json.Phone)-4:]

	var user model.User
	db.Where("phone", base64.StdEncoding.EncodeToString(aesEncrypted)).Find(&user)
	if user.ID == 0 {
		err := db.Create(&model.User{
			Name:   name,
			Phone:  json.Phone,
			Status: model.UserStatusActive,
		}).Error
		if err != nil {
			cosy.ErrHandler(c, err)
			return
		}
	}

	user.Name = name

	logger.Info("[User Login]", user.Name)

	user.UpdateLastActive()

	token, err := api.GenerateToken(&user)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// api return
	c.JSON(http.StatusOK, LoginResponse{
		Message: "ok",
		Token:   token,
		User:    &user,
	})
}
