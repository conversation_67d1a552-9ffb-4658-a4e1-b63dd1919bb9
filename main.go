package main

import (
	"flag"

	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/model/view"
	"git.uozi.org/uozi/potato-billing-api/pkg/limiter"
	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/router"
	"github.com/uozi-tech/cosy"
	mysql "github.com/uozi-tech/cosy-driver-mysql"
	"github.com/uozi-tech/cosy/settings"

	"git.uozi.org/uozi/potato-billing-api/pkg/migrate"
)

type Config struct {
	ConfPath string
	Maintain string
}

var cfg Config

func init() {
	flag.StringVar(&cfg.ConfPath, "config", "app.ini", "Specify the configuration file")
	flag.Parse()
}

func main() {
	cosy.RegisterModels(model.GenerateAllModel()...)

	cosy.RegisterMigration(migrate.Migrations)

	cosy.RegisterInitFunc(func() {
		db := cosy.InitDB(mysql.Open(settings.DataBaseSettings))
		query.Init(db)
		model.Use(db)
		view.CreateViews(db)
		limiter.Init()

		go billing.RunBillingServer()
	},
		model.InitRuntimeSettings,
		router.InitRouter,
	)

	cosy.Boot(cfg.ConfPath)
}
