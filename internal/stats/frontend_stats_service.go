package stats

import (
	"context"
	"fmt"
	"math"
	"time"

	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"github.com/uozi-tech/cosy"
)

// ModuleStatsResponse 模块统计响应结构
type ModuleStatsResponse struct {
	LLM ModuleStatItem `json:"llm"`
	TTS ModuleStatItem `json:"tts"`
	ASR ModuleStatItem `json:"asr"`
}

// ModuleStatItem 模块统计项
type ModuleStatItem struct {
	Calls          int64   `json:"calls"`
	Usage          int64   `json:"usage"`
	Cost           float64 `json:"cost"`
	AvgCostPerCall float64 `json:"avg_cost_per_call"`
}

// ModuleTrendResponse 模块趋势响应结构
type ModuleTrendResponse struct {
	Labels   []string             `json:"labels"`
	Datasets []ModuleTrendDataset `json:"datasets"`
}

// ModuleTrendDataset 模块趋势数据集
type ModuleTrendDataset struct {
	Label string    `json:"label"`
	Data  []float64 `json:"data"`
}

// BillingStatsResponse 计费统计概览响应结构
type BillingStatsResponse struct {
	TotalCalls        int64       `json:"total_calls"`
	TotalUsage        ModuleUsage `json:"total_usage"`
	TotalCost         float64     `json:"total_cost"`
	AvgCostPerCall    float64     `json:"avg_cost_per_call"`
	AppCount          int64       `json:"app_count"`
	TodayCost         float64     `json:"today_cost"`
	TodayCostGrowth   float64     `json:"today_cost_growth"`
	MonthlyCost       float64     `json:"monthly_cost"`
	MonthlyCostGrowth float64     `json:"monthly_cost_growth"`
	MonthlyCalls      int64       `json:"monthly_calls"`
	DailyAverageCost  float64     `json:"daily_average_cost"`
}

// ModuleUsage 模块使用量
type ModuleUsage struct {
	LLM int64 `json:"llm"`
	TTS int64 `json:"tts"`
	ASR int64 `json:"asr"`
}

// UsageTrendResponse 使用趋势响应结构
type UsageTrendResponse struct {
	Labels   []string    `json:"labels"`
	Datasets []TrendData `json:"datasets"`
}

// TrendData 趋势数据
type TrendData struct {
	Label string    `json:"label"`
	Data  []float64 `json:"data"`
}

// ServiceUsageResponse 服务使用分布响应结构
type ServiceUsageResponse struct {
	Labels   []string      `json:"labels"`
	Datasets []ServiceData `json:"datasets"`
}

// ServiceData 服务数据
type ServiceData struct {
	Data            []float64 `json:"data"`
	BackgroundColor []string  `json:"background_color"`
	BorderWidth     int       `json:"border_width"`
}

// BillingPeriodResponse 计费周期响应结构
type BillingPeriodResponse struct {
	ID        uint64  `json:"id"`
	Module    string  `json:"module"`
	Usage     int64   `json:"usage"`
	Cost      float64 `json:"cost"`
	Calls     int64   `json:"calls"`
	CreatedAt int64   `json:"created_at"`
}

// BillingPeriodsQuery 计费周期查询参数
type BillingPeriodsQuery struct {
	UserID    uint64 `form:"user_id"`
	AppID     uint64 `form:"app_id"`
	Period    string `form:"period"`
	StartDate string `form:"start_date"`
	EndDate   string `form:"end_date"`
	Page      int    `form:"page"`
	PageSize  int    `form:"pageSize"`
}

// ApplicationBillingResponse 应用计费数据响应结构
type ApplicationBillingResponse struct {
	Stats    BillingStatsResponse   `json:"stats"`
	Trends   []UsageTrendResponse   `json:"trends"`
	Services []ServiceUsageResponse `json:"services"`
}

// FrontendStatsService 前端统计服务
type FrontendStatsService struct {
	statService *StatService
}

// NewFrontendStatsService 创建前端统计服务
func NewFrontendStatsService(statService *StatService) *FrontendStatsService {
	return &FrontendStatsService{
		statService: statService,
	}
}

// GetModuleStats 获取用户的模块统计数据
func (fs *FrontendStatsService) GetModuleStats(ctx context.Context, userID, appID uint64, period, startDate, endDate, module string) (*ModuleStatsResponse, error) {
	// 如果没有指定appID，获取用户所有应用的统计
	if appID == 0 {
		userApps, err := fs.getUserApps(ctx, userID)
		if err != nil {
			return nil, fmt.Errorf("获取用户应用失败: %w", err)
		}

		// 如果用户没有应用，返回空统计
		if len(userApps) == 0 {
			return &ModuleStatsResponse{
				LLM: ModuleStatItem{},
				TTS: ModuleStatItem{},
				ASR: ModuleStatItem{},
			}, nil
		}

		// 聚合所有应用的统计数据
		var totalStats ModuleStatsWithParamsResponse
		for _, appID := range userApps {
			stats, err := fs.statService.GetModuleStatsWithParams(ctx, period, startDate, endDate, appID, module)
			if err != nil {
				continue // 忽略单个应用的错误
			}

			// 聚合数据
			totalStats.LLM.Calls += stats.LLM.Calls
			totalStats.LLM.Usage += stats.LLM.Usage
			totalStats.LLM.Cost += stats.LLM.Cost

			totalStats.TTS.Calls += stats.TTS.Calls
			totalStats.TTS.Usage += stats.TTS.Usage
			totalStats.TTS.Cost += stats.TTS.Cost

			totalStats.ASR.Calls += stats.ASR.Calls
			totalStats.ASR.Usage += stats.ASR.Usage
			totalStats.ASR.Cost += stats.ASR.Cost
		}

		// 计算平均值
		if totalStats.LLM.Calls > 0 {
			totalStats.LLM.AvgCostPerCall = totalStats.LLM.Cost / float64(totalStats.LLM.Calls)
		}
		if totalStats.TTS.Calls > 0 {
			totalStats.TTS.AvgCostPerCall = totalStats.TTS.Cost / float64(totalStats.TTS.Calls)
		}
		if totalStats.ASR.Calls > 0 {
			totalStats.ASR.AvgCostPerCall = totalStats.ASR.Cost / float64(totalStats.ASR.Calls)
		}

		// 转换为前端格式
		return &ModuleStatsResponse{
			LLM: ModuleStatItem{
				Calls:          totalStats.LLM.Calls,
				Usage:          totalStats.LLM.Usage,
				Cost:           totalStats.LLM.Cost,
				AvgCostPerCall: totalStats.LLM.AvgCostPerCall,
			},
			TTS: ModuleStatItem{
				Calls:          totalStats.TTS.Calls,
				Usage:          totalStats.TTS.Usage,
				Cost:           totalStats.TTS.Cost,
				AvgCostPerCall: totalStats.TTS.AvgCostPerCall,
			},
			ASR: ModuleStatItem{
				Calls:          totalStats.ASR.Calls,
				Usage:          totalStats.ASR.Usage,
				Cost:           totalStats.ASR.Cost,
				AvgCostPerCall: totalStats.ASR.AvgCostPerCall,
			},
		}, nil
	}

	// 指定了appID，直接获取该应用的统计
	stats, err := fs.statService.GetModuleStatsWithParams(ctx, period, startDate, endDate, appID, module)
	if err != nil {
		return nil, err
	}

	// 转换为前端格式
	return &ModuleStatsResponse{
		LLM: ModuleStatItem{
			Calls:          stats.LLM.Calls,
			Usage:          stats.LLM.Usage,
			Cost:           stats.LLM.Cost,
			AvgCostPerCall: stats.LLM.AvgCostPerCall,
		},
		TTS: ModuleStatItem{
			Calls:          stats.TTS.Calls,
			Usage:          stats.TTS.Usage,
			Cost:           stats.TTS.Cost,
			AvgCostPerCall: stats.TTS.AvgCostPerCall,
		},
		ASR: ModuleStatItem{
			Calls:          stats.ASR.Calls,
			Usage:          stats.ASR.Usage,
			Cost:           stats.ASR.Cost,
			AvgCostPerCall: stats.ASR.AvgCostPerCall,
		},
	}, nil
}

// GetModuleTrends 获取用户的模块趋势数据
func (fs *FrontendStatsService) GetModuleTrends(ctx context.Context, userID, appID uint64, period, metric, startDate, endDate string) (*ModuleTrendResponse, error) {
	// 如果没有指定appID，获取用户所有应用的趋势
	if appID == 0 {
		userApps, err := fs.getUserApps(ctx, userID)
		if err != nil {
			return nil, fmt.Errorf("获取用户应用失败: %w", err)
		}

		// 如果用户没有应用，返回空趋势
		if len(userApps) == 0 {
			return &ModuleTrendResponse{
				Labels:   []string{},
				Datasets: []ModuleTrendDataset{},
			}, nil
		}

		// 获取第一个应用的趋势作为基础
		baseTrends, err := fs.statService.GetModuleTrendsWithParams(ctx, period, metric, startDate, endDate, userApps[0])
		if err != nil {
			return nil, err
		}

		// 如果只有一个应用，直接返回
		if len(userApps) == 1 {
			// 转换为前端格式
			datasets := make([]ModuleTrendDataset, len(baseTrends.Datasets))
			for i, dataset := range baseTrends.Datasets {
				datasets[i] = ModuleTrendDataset{
					Label: dataset.Label,
					Data:  dataset.Data,
				}
			}
			return &ModuleTrendResponse{
				Labels:   baseTrends.Labels,
				Datasets: datasets,
			}, nil
		}

		// 聚合其他应用的数据
		for i := 1; i < len(userApps); i++ {
			trends, err := fs.statService.GetModuleTrendsWithParams(ctx, period, metric, startDate, endDate, userApps[i])
			if err != nil {
				continue // 忽略单个应用的错误
			}

			// 聚合数据集
			for j, dataset := range trends.Datasets {
				if j < len(baseTrends.Datasets) {
					for k, value := range dataset.Data {
						if k < len(baseTrends.Datasets[j].Data) {
							baseTrends.Datasets[j].Data[k] += value
						}
					}
				}
			}
		}

		// 转换为前端格式
		datasets := make([]ModuleTrendDataset, len(baseTrends.Datasets))
		for i, dataset := range baseTrends.Datasets {
			datasets[i] = ModuleTrendDataset{
				Label: dataset.Label,
				Data:  dataset.Data,
			}
		}
		return &ModuleTrendResponse{
			Labels:   baseTrends.Labels,
			Datasets: datasets,
		}, nil
	}

	// 指定了appID，直接获取该应用的趋势
	trends, err := fs.statService.GetModuleTrendsWithParams(ctx, period, metric, startDate, endDate, appID)
	if err != nil {
		return nil, err
	}

	// 转换为前端格式
	datasets := make([]ModuleTrendDataset, len(trends.Datasets))
	for i, dataset := range trends.Datasets {
		datasets[i] = ModuleTrendDataset{
			Label: dataset.Label,
			Data:  dataset.Data,
		}
	}
	return &ModuleTrendResponse{
		Labels:   trends.Labels,
		Datasets: datasets,
	}, nil
}

// getUserApps 获取用户的所有应用ID
func (fs *FrontendStatsService) getUserApps(ctx context.Context, userID uint64) ([]uint64, error) {
	db := cosy.UseDB(ctx)
	q := query.Use(db).App

	apps, err := q.Where(q.UserID.Eq(userID)).Find()
	if err != nil {
		return nil, err
	}

	var appIDs []uint64
	for _, app := range apps {
		appIDs = append(appIDs, app.ID)
	}

	return appIDs, nil
}

// IsUserApp 检查应用是否属于指定用户
func (fs *FrontendStatsService) IsUserApp(ctx context.Context, userID, appID uint64) bool {
	db := cosy.UseDB(ctx)
	q := query.Use(db).App

	count, err := q.Where(q.ID.Eq(appID), q.UserID.Eq(userID)).Count()
	if err != nil {
		return false
	}

	return count > 0
}

// GetBillingStats 获取计费统计概览
func (fs *FrontendStatsService) GetBillingStats(ctx context.Context, userID, appID uint64, period, startDate, endDate string) (*BillingStatsResponse, error) {
	// 获取模块统计数据
	moduleStats, err := fs.GetModuleStats(ctx, userID, appID, period, startDate, endDate, "")
	if err != nil {
		return nil, fmt.Errorf("获取模块统计数据失败: %w", err)
	}

	// 获取用户应用数量
	userApps, err := fs.getUserApps(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户应用失败: %w", err)
	}

	// 计算基础统计数据
	totalCalls := moduleStats.LLM.Calls + moduleStats.TTS.Calls + moduleStats.ASR.Calls
	totalCost := moduleStats.LLM.Cost + moduleStats.TTS.Cost + moduleStats.ASR.Cost

	var avgCostPerCall float64
	if totalCalls > 0 {
		avgCostPerCall = totalCost / float64(totalCalls)
	}

	// 计算今日费用和增长率
	todayCost, todayCostGrowth, err := fs.calculateTodayCostAndGrowth(ctx, userID, appID)
	if err != nil {
		// 不中断请求，使用默认值
		todayCost = 0
		todayCostGrowth = 0
	}

	// 计算月度费用和增长率
	monthlyCost, monthlyCostGrowth, err := fs.calculateMonthlyCostAndGrowth(ctx, userID, appID)
	if err != nil {
		// 不中断请求，使用默认值
		monthlyCost = 0
		monthlyCostGrowth = 0
	}

	// 计算日均费用
	dailyAverageCost, err := fs.calculateDailyAverageCost(ctx, period, startDate, endDate, userID, appID)
	if err != nil {
		// 不中断请求，使用默认值
		dailyAverageCost = 0
	}

	// 计算月度请求次数
	monthlyCalls, err := fs.calculateMonthlyCalls(ctx, userID, appID)
	if err != nil {
		// 不中断请求，使用默认值
		monthlyCalls = 0
	}

	// 构建完整的响应数据
	response := &BillingStatsResponse{
		TotalCalls: totalCalls,
		TotalUsage: ModuleUsage{
			LLM: moduleStats.LLM.Calls,
			TTS: moduleStats.TTS.Calls,
			ASR: moduleStats.ASR.Calls,
		},
		TotalCost:         totalCost,
		AvgCostPerCall:    avgCostPerCall,
		AppCount:          int64(len(userApps)),
		TodayCost:         todayCost,
		TodayCostGrowth:   todayCostGrowth,
		MonthlyCost:       monthlyCost,
		MonthlyCostGrowth: monthlyCostGrowth,
		MonthlyCalls:      monthlyCalls,
		DailyAverageCost:  dailyAverageCost,
	}

	return response, nil
}

// GetServiceUsage 获取服务使用分布
func (fs *FrontendStatsService) GetServiceUsage(ctx context.Context, userID, appID uint64, period, startDate, endDate string) (*ServiceUsageResponse, error) {
	// 获取模块统计数据
	stats, err := fs.GetModuleStats(ctx, userID, appID, period, startDate, endDate, "")
	if err != nil {
		return nil, fmt.Errorf("获取服务使用数据失败: %w", err)
	}

	// 构建服务分布数据
	response := &ServiceUsageResponse{
		Labels: []string{"LLM", "TTS", "ASR"},
		Datasets: []ServiceData{
			{
				Data: []float64{
					stats.LLM.Cost,
					stats.TTS.Cost,
					stats.ASR.Cost,
				},
				BackgroundColor: []string{
					"rgba(59, 130, 246, 0.8)",
					"rgba(16, 185, 129, 0.8)",
					"rgba(139, 92, 246, 0.8)",
				},
				BorderWidth: 0,
			},
		},
	}

	return response, nil
}

// GetBillingPeriods 获取计费周期数据
func (fs *FrontendStatsService) GetBillingPeriods(ctx context.Context, q *BillingPeriodsQuery) (map[string]interface{}, error) {
	// 解析时间范围
	var startTime, endTime time.Time
	var err error
	if q.StartDate != "" && q.EndDate != "" {
		startTime, err = time.Parse("2006-01-02", q.StartDate)
		if err != nil {
			return nil, fmt.Errorf("开始日期格式错误: %w", err)
		}
		endTime, err = time.Parse("2006-01-02", q.EndDate)
		if err != nil {
			return nil, fmt.Errorf("结束日期格式错误: %w", err)
		}
		endTime = endTime.Add(24*time.Hour - time.Second)
	} else {
		now := time.Now()
		switch q.Period {
		case "today":
			startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
			endTime = now
		case "7d":
			startTime = now.AddDate(0, 0, -7)
			endTime = now
		case "30d":
			startTime = now.AddDate(0, 0, -30)
			endTime = now
		case "90d":
			startTime = now.AddDate(0, 0, -90)
			endTime = now
		case "monthly":
			startTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
			endTime = now
		default:
			startTime = now.AddDate(0, 0, -30)
			endTime = now
		}
	}

	// 获取用户的应用ID列表
	var userAppIDs []uint64
	if q.AppID != 0 {
		userAppIDs = []uint64{q.AppID}
	} else {
		userAppIDs, err = fs.getUserApps(ctx, q.UserID)
		if err != nil {
			return nil, fmt.Errorf("获取用户应用失败: %w", err)
		}
	}

	// 如果用户没有应用，返回空结果
	if len(userAppIDs) == 0 {
		return map[string]interface{}{
			"data": []BillingPeriodResponse{},
			"pagination": map[string]interface{}{
				"total":        0,
				"per_page":     q.PageSize,
				"current_page": q.Page,
				"total_pages":  0,
			},
		}, nil
	}

	// 使用 query 包查询 Billing 表
	qb := query.Billing
	queryBuilder := qb.Where(
		qb.CreatedAt.Between(startTime.UnixMilli(), endTime.UnixMilli()),
		qb.AppID.In(userAppIDs...),
	)

	// 获取总数
	total, err := queryBuilder.Count()
	if err != nil {
		return nil, fmt.Errorf("获取计费记录总数失败: %w", err)
	}

	// 分页查询
	offset := (q.Page - 1) * q.PageSize
	billings, err := queryBuilder.
		Order(qb.CreatedAt.Desc()).
		Limit(q.PageSize).
		Offset(offset).
		Find()
	if err != nil {
		return nil, fmt.Errorf("获取计费记录失败: %w", err)
	}

	// 转换为响应格式
	var records []BillingPeriodResponse
	for _, billing := range billings {
		costFloat, _ := billing.Cost.Float64()
		records = append(records, BillingPeriodResponse{
			ID:        billing.ID,
			Module:    billing.Module,
			Usage:     billing.Usage,
			Cost:      costFloat,
			Calls:     billing.Calls,
			CreatedAt: billing.CreatedAt,
		})
	}

	response := map[string]interface{}{
		"data": records,
		"pagination": map[string]interface{}{
			"total_pages":  int(math.Ceil(float64(total) / float64(q.PageSize))),
			"total":        total,
			"per_page":     q.PageSize,
			"current_page": q.Page,
		},
	}

	return response, nil
}

// calculateTodayCostAndGrowth 计算今日费用和增长率
func (fs *FrontendStatsService) calculateTodayCostAndGrowth(ctx context.Context, userID, appID uint64) (float64, float64, error) {
	db := cosy.UseDB(ctx)
	now := time.Now()

	// 今日开始时间
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayStartMs := todayStart.UnixMilli()
	nowMs := now.UnixMilli()

	// 昨日开始和结束时间
	yesterdayStart := todayStart.AddDate(0, 0, -1)
	yesterdayStartMs := yesterdayStart.UnixMilli()
	yesterdayEndMs := todayStart.UnixMilli() - 1

	// 获取用户应用ID列表
	var userAppIDs []uint64
	var err error
	if appID != 0 {
		userAppIDs = []uint64{appID}
	} else {
		userAppIDs, err = fs.getUserApps(ctx, userID)
		if err != nil {
			return 0, 0, fmt.Errorf("获取用户应用失败: %w", err)
		}
	}

	if len(userAppIDs) == 0 {
		return 0, 0, nil
	}

	// 计算今日费用
	var todayCost float64
	err = db.Model(&model.UsageLog{}).
		Where("app_id IN ? AND created_at BETWEEN ? AND ?", userAppIDs, todayStartMs, nowMs).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&todayCost).Error
	if err != nil {
		return 0, 0, fmt.Errorf("计算今日费用失败: %w", err)
	}

	// 计算昨日费用
	var yesterdayCost float64
	err = db.Model(&model.UsageLog{}).
		Where("app_id IN ? AND created_at BETWEEN ? AND ?", userAppIDs, yesterdayStartMs, yesterdayEndMs).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&yesterdayCost).Error
	if err != nil {
		return 0, 0, fmt.Errorf("计算昨日费用失败: %w", err)
	}

	// 计算增长率
	var growth float64
	if yesterdayCost > 0 {
		growth = ((todayCost - yesterdayCost) / yesterdayCost) * 100
	} else if todayCost > 0 {
		growth = 100 // 如果昨日费用为0，今日有费用，则增长100%
	}

	return todayCost, growth, nil
}

// calculateMonthlyCostAndGrowth 计算月度费用和增长率
func (fs *FrontendStatsService) calculateMonthlyCostAndGrowth(ctx context.Context, userID, appID uint64) (float64, float64, error) {
	db := cosy.UseDB(ctx)
	now := time.Now()

	// 本月开始时间
	thisMonthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	thisMonthStartMs := thisMonthStart.UnixMilli()
	nowMs := now.UnixMilli()

	// 上月开始和结束时间
	lastMonthStart := thisMonthStart.AddDate(0, -1, 0)
	lastMonthStartMs := lastMonthStart.UnixMilli()
	lastMonthEndMs := thisMonthStart.UnixMilli() - 1

	// 获取用户应用ID列表
	var userAppIDs []uint64
	var err error
	if appID != 0 {
		userAppIDs = []uint64{appID}
	} else {
		userAppIDs, err = fs.getUserApps(ctx, userID)
		if err != nil {
			return 0, 0, fmt.Errorf("获取用户应用失败: %w", err)
		}
	}

	if len(userAppIDs) == 0 {
		return 0, 0, nil
	}

	// 计算本月费用
	var thisMonthCost float64
	err = db.Model(&model.UsageLog{}).
		Where("app_id IN ? AND created_at BETWEEN ? AND ?", userAppIDs, thisMonthStartMs, nowMs).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&thisMonthCost).Error
	if err != nil {
		return 0, 0, fmt.Errorf("计算本月费用失败: %w", err)
	}

	// 计算上月费用
	var lastMonthCost float64
	err = db.Model(&model.UsageLog{}).
		Where("app_id IN ? AND created_at BETWEEN ? AND ?", userAppIDs, lastMonthStartMs, lastMonthEndMs).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&lastMonthCost).Error
	if err != nil {
		return 0, 0, fmt.Errorf("计算上月费用失败: %w", err)
	}

	// 计算增长率
	var growth float64
	if lastMonthCost > 0 {
		growth = ((thisMonthCost - lastMonthCost) / lastMonthCost) * 100
	} else if thisMonthCost > 0 {
		growth = 100 // 如果上月费用为0，本月有费用，则增长100%
	}

	return thisMonthCost, growth, nil
}

// calculateDailyAverageCost 计算日均费用
func (fs *FrontendStatsService) calculateDailyAverageCost(ctx context.Context, period, startDate, endDate string, userID, appID uint64) (float64, error) {
	db := cosy.UseDB(ctx)

	// 解析时间范围
	var startTime, endTime time.Time
	var err error
	if startDate != "" && endDate != "" {
		startTime, err = time.Parse("2006-01-02", startDate)
		if err != nil {
			return 0, fmt.Errorf("开始日期格式错误: %w", err)
		}
		endTime, err = time.Parse("2006-01-02", endDate)
		if err != nil {
			return 0, fmt.Errorf("结束日期格式错误: %w", err)
		}
		endTime = endTime.Add(24*time.Hour - time.Second)
	} else {
		now := time.Now()
		switch period {
		case "day":
			startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
			endTime = now
		case "week":
			startTime = now.AddDate(0, 0, -7)
			endTime = now
		case "month":
			startTime = now.AddDate(0, -1, 0)
			endTime = now
		case "year":
			startTime = now.AddDate(-1, 0, 0)
			endTime = now
		default:
			startTime = now.AddDate(0, -1, 0) // 默认一个月
			endTime = now
		}
	}

	// 获取用户应用ID列表
	var userAppIDs []uint64
	if appID != 0 {
		userAppIDs = []uint64{appID}
	} else {
		userAppIDs, err = fs.getUserApps(ctx, userID)
		if err != nil {
			return 0, fmt.Errorf("获取用户应用失败: %w", err)
		}
	}

	if len(userAppIDs) == 0 {
		return 0, nil
	}

	// 计算总费用
	var totalCost float64
	err = db.Model(&model.UsageLog{}).
		Where("app_id IN ? AND created_at BETWEEN ? AND ?", userAppIDs, startTime.UnixMilli(), endTime.UnixMilli()).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&totalCost).Error
	if err != nil {
		return 0, fmt.Errorf("计算总费用失败: %w", err)
	}

	// 计算天数
	days := int(endTime.Sub(startTime).Hours() / 24)
	if days <= 0 {
		days = 1
	}

	// 计算日均费用
	dailyAverage := totalCost / float64(days)

	return dailyAverage, nil
}

// calculateMonthlyCalls 计算月度请求次数
func (fs *FrontendStatsService) calculateMonthlyCalls(ctx context.Context, userID, appID uint64) (int64, error) {
	db := cosy.UseDB(ctx)
	now := time.Now()

	// 本月开始时间
	thisMonthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	thisMonthStartMs := thisMonthStart.UnixMilli()
	nowMs := now.UnixMilli()

	// 获取用户应用ID列表
	var userAppIDs []uint64
	var err error
	if appID != 0 {
		userAppIDs = []uint64{appID}
	} else {
		userAppIDs, err = fs.getUserApps(ctx, userID)
		if err != nil {
			return 0, fmt.Errorf("获取用户应用失败: %w", err)
		}
	}

	if len(userAppIDs) == 0 {
		return 0, nil
	}

	// 计算本月请求次数
	var monthlyCalls int64
	err = db.Model(&model.UsageLog{}).
		Where("app_id IN ? AND created_at BETWEEN ? AND ?", userAppIDs, thisMonthStartMs, nowMs).
		Count(&monthlyCalls).Error
	if err != nil {
		return 0, fmt.Errorf("计算月度请求次数失败: %w", err)
	}

	return monthlyCalls, nil
}
