package app

import (
	"context"
	"time"

	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"github.com/uozi-tech/cosy"
)

// CreateAppRequest 创建应用请求结构
type CreateAppRequest struct {
	Name    string `json:"name" binding:"required"`
	UserID  uint64 `json:"user_id,string" binding:"required"`
	Comment string `json:"comment"`
}

// UpdateAppRequest 更新应用请求结构
type UpdateAppRequest struct {
	Name    string `json:"name"`
	Comment string `json:"comment"`
	Status  string `json:"status"`
}

// AppStatusResponse 应用状态响应结构
type AppStatusResponse struct {
	AppID     uint64                      `json:"app_id"`
	Status    string                      `json:"status"`
	Available bool                        `json:"available"`
	Quotas    []*model.QuotaPackageRecord `json:"quotas"`
	User      *model.User                 `json:"user,omitempty"`
	CreatedAt int64                       `json:"created_at"`
	UpdatedAt int64                       `json:"updated_at"`
}

// AppListQuery 应用列表查询参数
type AppListQuery struct {
	UserID   uint64 `form:"user_id"`
	Search   string `form:"search"`
	Status   string `form:"status"`
	Page     int    `form:"page"`
	PageSize int    `form:"pageSize"`
}

// AppStats 应用统计信息
type AppStats struct {
	TotalRequests   int64   `json:"total_requests"`
	TodayRequests   int64   `json:"today_requests"`
	TotalCost       float64 `json:"total_cost"`
	TodayCost       float64 `json:"today_cost"`
	LastRequestTime int64   `json:"last_request_time"`
}

// AppWithStats 带统计信息的应用
type AppWithStats struct {
	*model.App
	Stats *AppStatsData `json:"stats"`
}

// AppListResponse 应用列表响应
type AppListResponse struct {
	Data       []AppWithStats         `json:"data"`
	Pagination map[string]interface{} `json:"pagination"`
}

// GetAppList 获取应用列表
func GetAppList(ctx context.Context, queryParams *AppListQuery) (*AppListResponse, error) {
	db := cosy.UseDB(ctx)
	q := query.Use(db).App

	queryBuilder := q.Select(q.ALL)

	// 添加用户过滤
	if queryParams.UserID > 0 {
		queryBuilder = queryBuilder.Where(q.UserID.Eq(queryParams.UserID))
	}

	// 添加搜索条件
	if queryParams.Search != "" {
		queryBuilder = queryBuilder.Where(
			q.Name.Like("%" + queryParams.Search + "%"),
		).Or(
			q.Comment.Like("%" + queryParams.Search + "%"),
		)
	}

	// 添加状态过滤
	if queryParams.Status != "" {
		queryBuilder = queryBuilder.Where(q.Status.Eq(queryParams.Status))
	}

	// 获取总数
	total, err := queryBuilder.Count()
	if err != nil {
		return nil, err
	}

	// 设置分页参数
	if queryParams.Page < 1 {
		queryParams.Page = 1
	}
	if queryParams.PageSize < 1 || queryParams.PageSize > 100 {
		queryParams.PageSize = 20
	}

	// 分页查询
	offset := (queryParams.Page - 1) * queryParams.PageSize
	apps, err := queryBuilder.
		Preload(q.User).
		Order(q.CreatedAt.Desc()).
		Limit(queryParams.PageSize).
		Offset(offset).
		Find()
	if err != nil {
		return nil, err
	}

	// 为每个应用添加统计数据
	appsWithStats := make([]AppWithStats, 0, len(apps))
	for _, app := range apps {
		stats, err := getAppStats(ctx, app.ID)
		if err != nil {
			return nil, err
		}
		appWithStats := AppWithStats{
			App:   app,
			Stats: stats,
		}
		appsWithStats = append(appsWithStats, appWithStats)
	}

	// 构建响应
	response := &AppListResponse{
		Data: appsWithStats,
		Pagination: map[string]interface{}{
			"total":        total,
			"per_page":     queryParams.PageSize,
			"current_page": queryParams.Page,
			"total_pages":  (total + int64(queryParams.PageSize) - 1) / int64(queryParams.PageSize),
		},
	}

	return response, nil
}

// GetApp 获取应用详情
func GetApp(ctx context.Context, appID uint64, userID uint64) (*model.App, error) {
	q := query.App

	queryBuilder := q.Preload(q.User).Where(q.ID.Eq(appID))

	// 如果指定了用户ID，添加用户过滤
	if userID > 0 {
		queryBuilder = queryBuilder.Where(q.UserID.Eq(userID))
	}

	app, err := queryBuilder.First()
	if err != nil {
		return nil, err
	}

	return app, nil
}

// CreateApp 创建应用
func CreateApp(ctx context.Context, operatorID uint64, req *CreateAppRequest) (*model.App, error) {
	// 创建新的应用记录
	app := &model.App{
		Name:       req.Name,
		Status:     "ok",
		UserID:     req.UserID,
		Comment:    req.Comment,
		OperatorID: operatorID,
	}

	// 调用 billing 服务创建应用
	billingService := billing.GetBillingService()
	err := billingService.GetAppService().CreateApp(ctx, app)
	if err != nil {
		return nil, err
	}

	// 重新查询以获取完整信息
	result, err := query.App.
		Preload(query.App.User).
		Where(query.App.ID.Eq(app.ID)).
		First()
	if err != nil {
		return nil, err
	}

	return result, nil
}

// UpdateApp 更新应用
func UpdateApp(ctx context.Context, appID uint64, userID uint64, req *UpdateAppRequest) (*model.App, error) {
	q := query.App

	queryBuilder := q.Where(q.ID.Eq(appID))

	// 如果指定了用户ID，添加用户过滤
	if userID > 0 {
		queryBuilder = queryBuilder.Where(q.UserID.Eq(userID))
	}

	// 检查应用是否存在
	_, err := queryBuilder.First()
	if err != nil {
		return nil, err
	}

	// 构建更新数据
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Comment != "" {
		updates["comment"] = req.Comment
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}

	// 更新应用信息
	_, err = queryBuilder.Updates(updates)
	if err != nil {
		return nil, err
	}

	// 返回更新后的数据
	result, err := q.Preload(q.User).Where(q.ID.Eq(appID)).First()
	if err != nil {
		return nil, err
	}

	return result, nil
}

// DeleteApp 删除应用
func DeleteApp(ctx context.Context, appID uint64, userID uint64) error {
	q := query.App

	queryBuilder := q.Where(q.ID.Eq(appID))

	// 如果指定了用户ID，添加用户过滤
	if userID > 0 {
		queryBuilder = queryBuilder.Where(q.UserID.Eq(userID))
	}

	// 检查应用是否存在
	_, err := queryBuilder.First()
	if err != nil {
		return err
	}

	// 删除应用
	_, err = queryBuilder.Delete()
	if err != nil {
		return err
	}

	return nil
}

// RegenerateAPIKey 重新生成API Key
func RegenerateAPIKey(ctx context.Context, appID uint64, userID uint64) (*model.App, error) {
	q := query.App

	queryBuilder := q.Where(q.ID.Eq(appID))

	// 如果指定了用户ID，添加用户过滤
	if userID > 0 {
		queryBuilder = queryBuilder.Where(q.UserID.Eq(userID))
	}

	// 检查应用是否存在
	_, err := queryBuilder.First()
	if err != nil {
		return nil, err
	}

	// 生成新的API Key
	app := &model.App{}
	newAPIKey := app.GenerateAPIKey()

	_, err = queryBuilder.Update(q.APIKey, newAPIKey)
	if err != nil {
		return nil, err
	}

	// 返回更新后的数据
	result, err := q.Preload(q.User).Where(q.ID.Eq(appID)).First()
	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetAppStatus 获取应用状态
func GetAppStatus(ctx context.Context, appID uint64) (*AppStatusResponse, error) {
	// 查询应用记录
	app, err := query.App.
		Preload(query.App.User).
		Where(query.App.ID.Eq(appID)).
		First()
	if err != nil {
		return nil, err
	}

	// 查询该应用的专属配额信息
	var quotas []*model.QuotaPackageRecord
	err = query.QuotaPackageRecord.UnderlyingDB().
		Where("(api_key IN ? OR api_key IS NULL)", []string{app.APIKey, ""}).
		Where("user_id = ?", app.UserID).
		Find(&quotas).Error
	if err != nil {
		return nil, err
	}

	// 计算是否可用（只检查对应类型的配额）
	available := app.Status == "ok"
	if len(quotas) > 0 {
		for _, quota := range quotas {
			if quota.Quota > 0 && quota.Used >= quota.Quota && app.User.Balance <= 0 {
				available = false
				break
			}
		}
	} else {
		// 没有该类型的配额，检查用户余额
		if app.User == nil || app.User.Balance <= 0 {
			available = false
		}
	}

	response := &AppStatusResponse{
		AppID:     app.ID,
		Status:    app.Status,
		Available: available,
		Quotas:    quotas,
		User:      app.User,
		CreatedAt: app.CreatedAt,
		UpdatedAt: app.UpdatedAt,
	}

	return response, nil
}

// GetAppOverviewStats 获取应用概览统计
func GetAppOverviewStats(ctx context.Context) (interface{}, error) {
	// 获取统计服务实例
	statService := billing.GetStatServiceInstance()
	if statService == nil {
		return nil, ErrStatServiceUnavailable
	}

	// 调用统计服务获取数据
	stats, err := statService.GetAppOverviewStats(ctx)
	if err != nil {
		return nil, err
	}

	return stats, nil
}

// AppStatsData 应用统计数据
type AppStatsData struct {
	TodayCalls   int64   `json:"today_calls"`   // 今日调用次数
	TodayCost    float64 `json:"today_cost"`    // 今日费用
	MonthlyCalls int64   `json:"monthly_calls"` // 本月调用次数
	MonthlyCost  float64 `json:"monthly_cost"`  // 本月费用
}

// getAppStats 获取单个应用的统计数据
func getAppStats(ctx context.Context, appID uint64) (*AppStatsData, error) {
	db := cosy.UseDB(ctx)
	now := time.Now()

	// 今日开始时间
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayStartMs := todayStart.UnixMilli()
	nowMs := now.UnixMilli()

	// 本月开始时间
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	monthStartMs := monthStart.UnixMilli()

	stats := AppStatsData{}

	// 查询今日统计
	var todayResult struct {
		TotalCalls int64
		TotalCost  float64
	}

	err := db.Model(&model.UsageLog{}).
		Select("COUNT(*) as total_calls, COALESCE(SUM(cost), 0) as total_cost").
		Where("app_id = ? AND created_at >= ? AND created_at <= ?", appID, todayStartMs, nowMs).
		Scan(&todayResult).Error

	if err != nil {
		return nil, err
	} else {
		stats.TodayCalls = todayResult.TotalCalls
		stats.TodayCost = todayResult.TotalCost
	}

	// 查询本月统计
	var monthlyResult struct {
		TotalCalls int64
		TotalCost  float64
	}

	err = db.Model(&model.UsageLog{}).
		Select("COUNT(*) as total_calls, COALESCE(SUM(cost), 0) as total_cost").
		Where("app_id = ? AND created_at >= ? AND created_at <= ?", appID, monthStartMs, nowMs).
		Scan(&monthlyResult).Error

	if err != nil {
		return nil, err
	} else {
		stats.MonthlyCalls = monthlyResult.TotalCalls
		stats.MonthlyCost = monthlyResult.TotalCost
	}

	return &stats, nil
}
