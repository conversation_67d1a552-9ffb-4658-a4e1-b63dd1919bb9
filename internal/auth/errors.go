package auth

import (
	"github.com/uozi-tech/cosy"
)

const (
	ErrCodeUsernameOrPasswordIncorrect = 4031
	ErrCodeMaxAttempts                 = 4291
	ErrCodeUserWaitForValidation       = 4032
	ErrCodeUserBlocked                 = 4033
	ErrCodeUserNotAllowed              = 4034
	ErrCodeUserNotFound                = 4041
	ErrCodeUserAlreadyExists           = 4091
	ErrCodeUserNotActivated            = 4035
	ErrCodeUserNotVerified             = 4036
	ErrCodeSendPhoneValidationCode     = 4037
)

var (
	e = cosy.NewErrorScope("auth")

	ErrUsernameOrPasswordIncorrect = e.New(ErrCodeUsernameOrPasswordIncorrect, "username or password incorrect")
	ErrMaxAttempts                 = e.New(ErrCodeMaxAttempts, "max attempts")
	ErrUserWaitForValidation       = e.New(ErrCodeUserWaitForValidation, "user wait for validation")
	ErrUserBlocked                 = e.New(ErrCodeUserBlocked, "user blocked")
	ErrUserNotAllowed              = e.New(ErrCodeUserNotAllowed, "user not allowed")
	ErrUserNotFound                = e.New(ErrCodeUserNotFound, "user not found")
	ErrUserAlreadyExists           = e.New(ErrCodeUserAlreadyExists, "user already exists")
	ErrUserNotActivated            = e.New(ErrCodeUserNotActivated, "user not activated")
	ErrUserNotVerified             = e.New(ErrCodeUserNotVerified, "user not verified")
	ErrSendPhoneValidationCode     = e.New(ErrCodeSendPhoneValidationCode, "send phone validation code")
)
