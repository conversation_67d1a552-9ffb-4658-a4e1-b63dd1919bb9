package auth

import (
	"context"
	"errors"
	"time"

	"git.uozi.org/uozi/potato-billing-api/internal/runtime_settings"
	"git.uozi.org/uozi/potato-billing-api/internal/user"
	"git.uozi.org/uozi/potato-billing-api/model"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy/logger"
	"github.com/uozi-tech/cosy/redis"
)

// LoginRequest 登录请求结构
type LoginRequest struct {
	Email    string `json:"email" binding:"required"`
	Password string `json:"password" binding:"required"`
	Source   string `json:"source" binding:"required"`
}

// LoginResponse 登录响应结构
type LoginResponse struct {
	Message string      `json:"message"`
	Error   string      `json:"error,omitempty"`
	Code    int         `json:"code"`
	Token   string      `json:"token,omitempty"`
	User    *model.User `json:"user,omitempty"`
}

// Login 用户登录业务逻辑
func Login(ctx context.Context, clientIP, fingerprint string, req *LoginRequest) (*LoginResponse, error) {
	// 获取分布式锁
	lock, err := redis.ObtainLock("login:"+clientIP+fingerprint, 10*time.Millisecond, nil)
	if err != nil {
		return &LoginResponse{
			Message: "Too many requests",
			Code:    429,
		}, nil
	}
	defer lock.Release(ctx)

	// 检查登录失败次数
	var loginFailedKey = "login_failed:" + clientIP
	auth := runtime_settings.GetAuthSettings()

	countStr, err := redis.Get(loginFailedKey)
	if err != nil {
		_ = redis.Set(loginFailedKey, 0,
			time.Duration(
				lo.Max([]int{auth.BanThresholdMinutes, 1}),
			)*time.Minute)
	}
	failedCount := cast.ToInt(countStr)

	if auth.MaxAttempts > 0 && failedCount >= auth.MaxAttempts {
		return nil, ErrMaxAttempts
	}

	// 尝试登录
	u, err := user.Login(req.Email, req.Password, req.Source)
	if err != nil {
		logger.Error("Login failed", "error", err, "email", req.Email)

		// 增加失败次数
		_, _ = redis.Incr(loginFailedKey)

		switch {
		case errors.Is(err, user.ErrPasswordIncorrect):
			return nil, ErrUsernameOrPasswordIncorrect
		case errors.Is(err, user.ErrUserBlocked):
			return nil, ErrUserBlocked
		case errors.Is(err, user.ErrUserNotAllowed):
			return nil, ErrUserNotAllowed
		default:
			return nil, err
		}
	}

	logger.Info("[User Login]", "user", u.Name, "email", u.Email)

	// 更新最后活跃时间
	u.UpdateLastActive()

	return &LoginResponse{
		Message: "ok",
		Code:    200,
		User:    u,
	}, nil
}

// Logout 用户登出业务逻辑
func Logout(token string) error {
	return redis.Del("token:" + token)
}

// PhoneLoginRequest 手机登录请求结构
type PhoneLoginRequest struct {
	Phone string `json:"phone" binding:"required"`
	Code  string `json:"code" binding:"required"`
}

// PhoneLogin 手机号登录业务逻辑
func PhoneLogin(ctx context.Context, req *PhoneLoginRequest) (*LoginResponse, error) {
	// 验证手机验证码
	// 这里需要根据实际的验证码验证逻辑来实现
	// 暂时返回未实现错误
	return nil, errors.New("phone login not implemented yet")
}

// ValidPhoneRequest 验证手机号请求结构
type ValidPhoneRequest struct {
	Phone string `json:"phone" binding:"required"`
}

// SendPhoneValidationCode 发送手机验证码
func SendPhoneValidationCode(ctx context.Context, req *ValidPhoneRequest) error {
	// 发送验证码逻辑
	// 这里需要根据实际的短信服务来实现
	// 暂时返回未实现错误
	return ErrSendPhoneValidationCode
}
