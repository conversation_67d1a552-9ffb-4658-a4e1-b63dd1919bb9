package billing

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/types"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"
)

// CreateRechargeRequest 创建充值记录请求
type CreateRechargeRequest struct {
	UserID      uint64  `json:"user_id,string" binding:"required"`
	Amount      float64 `json:"amount" binding:"required,min=0.01"`
	Type        string  `json:"type,omitempty"`
	Description string  `json:"description,omitempty"`
}

// RechargeStatsResponse 充值统计响应
type RechargeStatsResponse struct {
	TotalAmount    float64 `json:"total_amount"`
	TotalCount     int64   `json:"total_count"`
	TodayAmount    float64 `json:"today_amount"`
	TodayCount     int64   `json:"today_count"`
	MonthAmount    float64 `json:"month_amount"`
	MonthCount     int64   `json:"month_count"`
	PendingAmount  float64 `json:"pending_amount"`
	PendingCount   int64   `json:"pending_count"`
	CompletedCount int64   `json:"completed_count"`
	CancelledCount int64   `json:"cancelled_count"`
}

// UserBalanceInfo 用户余额信息
type UserBalanceInfo struct {
	ID           string  `json:"id"`
	Name         string  `json:"name"`
	Email        string  `json:"email"`
	Balance      float64 `json:"balance"`
	LastRecharge string  `json:"last_recharge"`
}

// RechargeService 充值服务
type RechargeService struct{}

var (
	rechargeServiceInstance *RechargeService
	rechargeServiceOnce     sync.Once
)

// GetRechargeService 获取充值服务单例
func GetRechargeService() *RechargeService {
	rechargeServiceOnce.Do(func() {
		rechargeServiceInstance = &RechargeService{}
		logger.Info("RechargeService singleton initialized")
	})
	return rechargeServiceInstance
}

// CreateRecharge 创建充值记录（管理员充值）
func (rs *RechargeService) CreateRecharge(ctx context.Context, req *CreateRechargeRequest, operatorID uint64) error {
	// 检查用户是否存在
	user, err := rs.validateUser(req.UserID)
	if err != nil {
		return err
	}

	// 使用事务处理充值
	return rs.processRechargeTransaction(ctx, user, req, operatorID)
}

// validateUser 验证用户是否存在
func (rs *RechargeService) validateUser(userID uint64) (*model.User, error) {
	user, err := query.User.Where(query.User.ID.Eq(userID)).First()
	if err != nil {
		return nil, fmt.Errorf("用户不存在: %w", err)
	}
	return user, nil
}

// processRechargeTransaction 处理充值事务
func (rs *RechargeService) processRechargeTransaction(ctx context.Context, user *model.User, req *CreateRechargeRequest, operatorID uint64) error {
	db := cosy.UseDB(ctx)
	return db.Transaction(func(tx *gorm.DB) error {
		// 创建充值记录
		rechargeRecord := rs.buildRechargeRecord(req, operatorID)
		if err := tx.Create(rechargeRecord).Error; err != nil {
			return fmt.Errorf("创建充值记录失败: %w", err)
		}

		// 增加用户余额
		if err := rs.updateUserBalance(tx, user, req.Amount); err != nil {
			return err
		}

		return nil
	})
}

// buildRechargeRecord 构建充值记录
func (rs *RechargeService) buildRechargeRecord(req *CreateRechargeRequest, operatorID uint64) *model.RechargeRecord {
	return &model.RechargeRecord{
		UserID:      req.UserID,
		Amount:      req.Amount,
		Type:        req.Type,
		Status:      types.RechargeStatusCompleted, // 管理员充值直接完成
		Description: req.Description,
		OperatorID:  operatorID,
	}
}

// updateUserBalance 更新用户余额
func (rs *RechargeService) updateUserBalance(tx *gorm.DB, user *model.User, amount float64) error {
	err := tx.Model(user).
		Update("balance", gorm.Expr("balance + ?", amount)).
		Error
	if err != nil {
		return fmt.Errorf("更新用户余额失败: %w", err)
	}
	return nil
}

// ConfirmRecharge 确认充值（用于处理第三方支付回调）
func (rs *RechargeService) ConfirmRecharge(ctx context.Context, id uint64) error {
	// 获取充值记录
	rechargeRecord, user, err := rs.getRechargeAndUser(id)
	if err != nil {
		return err
	}

	if rechargeRecord.Status != types.RechargeStatusPending {
		return fmt.Errorf("充值记录状态不正确，当前状态: %s", rechargeRecord.Status)
	}

	// 使用事务处理确认
	if err := rs.processConfirmTransaction(ctx, rechargeRecord, user); err != nil {
		return err
	}

	// 充值确认后，检查并恢复被阻塞的应用状态
	rs.restoreAppStatusAfterRecharge(ctx, rechargeRecord.UserID)
	return nil
}

// getRechargeAndUser 获取充值记录和用户信息
func (rs *RechargeService) getRechargeAndUser(rechargeID uint64) (*model.RechargeRecord, *model.User, error) {
	rechargeRecord, err := query.RechargeRecord.Where(query.RechargeRecord.ID.Eq(rechargeID)).First()
	if err != nil {
		return nil, nil, fmt.Errorf("充值记录不存在: %w", err)
	}

	user, err := query.User.Where(query.User.ID.Eq(rechargeRecord.UserID)).First()
	if err != nil {
		return nil, nil, fmt.Errorf("用户不存在: %w", err)
	}

	return rechargeRecord, user, nil
}

// processConfirmTransaction 处理确认事务
func (rs *RechargeService) processConfirmTransaction(ctx context.Context, rechargeRecord *model.RechargeRecord, user *model.User) error {
	db := cosy.UseDB(ctx)
	return db.Transaction(func(tx *gorm.DB) error {
		// 更新充值记录状态
		if err := rs.updateRechargeStatus(tx, rechargeRecord); err != nil {
			return err
		}

		// 增加用户余额
		if err := rs.updateUserBalance(tx, user, rechargeRecord.Amount); err != nil {
			return err
		}

		return nil
	})
}

// updateRechargeStatus 更新充值记录状态
func (rs *RechargeService) updateRechargeStatus(tx *gorm.DB, rechargeRecord *model.RechargeRecord) error {
	err := tx.Model(rechargeRecord).Update("status", types.RechargeStatusCompleted).Error
	if err != nil {
		return fmt.Errorf("更新充值记录状态失败: %w", err)
	}
	return nil
}

// restoreAppStatusAfterRecharge 充值后恢复应用状态
func (rs *RechargeService) restoreAppStatusAfterRecharge(ctx context.Context, userID uint64) {
	appService := GetAppService()
	if appService != nil {
		err := appService.CheckAndRestoreAppStatus(ctx, userID, 0, "")
		if err != nil {
			logger.Error("确认充值后恢复应用状态失败", "error", err, "userID", userID)
		} else {
			logger.Info("确认充值后成功恢复应用状态", "userID", userID)
		}
	}
}

// CancelRecharge 取消充值
func (rs *RechargeService) CancelRecharge(ctx context.Context, id uint64) error {
	// 获取充值记录
	rechargeRecord, err := query.RechargeRecord.Where(query.RechargeRecord.ID.Eq(id)).First()
	if err != nil {
		return fmt.Errorf("充值记录不存在: %w", err)
	}

	if rechargeRecord.Status != types.RechargeStatusPending {
		return fmt.Errorf("只能取消待处理的充值记录，当前状态: %s", rechargeRecord.Status)
	}

	// 更新状态为已取消
	_, err = query.RechargeRecord.Where(query.RechargeRecord.ID.Eq(id)).Update(query.RechargeRecord.Status, types.RechargeStatusCancelled)
	if err != nil {
		return fmt.Errorf("取消充值失败: %w", err)
	}

	return nil
}

// GetRechargeStats 获取充值统计
func (rs *RechargeService) GetRechargeStats(ctx context.Context) (*RechargeStatsResponse, error) {
	db := cosy.UseDB(ctx)
	stats := &RechargeStatsResponse{}

	// 获取各种统计数据
	if err := rs.getTotalStats(db, stats); err != nil {
		return nil, err
	}

	if err := rs.getTodayStats(db, stats); err != nil {
		return nil, err
	}

	if err := rs.getMonthStats(db, stats); err != nil {
		return nil, err
	}

	if err := rs.getPendingStats(db, stats); err != nil {
		return nil, err
	}

	if err := rs.getStatusCounts(db, stats); err != nil {
		return nil, err
	}

	return stats, nil
}

// getTotalStats 获取总充值统计
func (rs *RechargeService) getTotalStats(db *gorm.DB, stats *RechargeStatsResponse) error {
	return db.Model(&model.RechargeRecord{}).
		Where("status = ?", types.RechargeStatusCompleted).
		Select("COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as total_count").
		Scan(stats).Error
}

// getTodayStats 获取今日统计
func (rs *RechargeService) getTodayStats(db *gorm.DB, stats *RechargeStatsResponse) error {
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	return db.Model(&model.RechargeRecord{}).
		Where("status = ? AND created_at >= ?", types.RechargeStatusCompleted, todayStart.UnixMilli()).
		Select("COALESCE(SUM(amount), 0) as today_amount, COUNT(*) as today_count").
		Scan(stats).Error
}

// getMonthStats 获取本月统计
func (rs *RechargeService) getMonthStats(db *gorm.DB, stats *RechargeStatsResponse) error {
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	return db.Model(&model.RechargeRecord{}).
		Where("status = ? AND created_at >= ?", types.RechargeStatusCompleted, monthStart.UnixMilli()).
		Select("COALESCE(SUM(amount), 0) as month_amount, COUNT(*) as month_count").
		Scan(stats).Error
}

// getPendingStats 获取待处理统计
func (rs *RechargeService) getPendingStats(db *gorm.DB, stats *RechargeStatsResponse) error {
	return db.Model(&model.RechargeRecord{}).
		Where("status = ?", types.RechargeStatusPending).
		Select("COALESCE(SUM(amount), 0) as pending_amount, COUNT(*) as pending_count").
		Scan(stats).Error
}

// getStatusCounts 获取状态统计
func (rs *RechargeService) getStatusCounts(db *gorm.DB, stats *RechargeStatsResponse) error {
	var statusCounts []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}

	err := db.Model(&model.RechargeRecord{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Scan(&statusCounts).Error
	if err != nil {
		return fmt.Errorf("获取状态统计失败: %w", err)
	}

	for _, sc := range statusCounts {
		switch sc.Status {
		case types.RechargeStatusCompleted:
			stats.CompletedCount = sc.Count
		case types.RechargeStatusCancelled:
			stats.CancelledCount = sc.Count
		}
	}

	return nil
}

// GetUserBalances 获取用户余额信息
func (rs *RechargeService) GetUserBalances(ctx context.Context, page, pageSize int) ([]UserBalanceInfo, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	db := cosy.UseDB(ctx)

	// 获取总数
	total, err := rs.getUserCount(db)
	if err != nil {
		return nil, 0, err
	}

	// 分页查询用户余额信息
	users, err := rs.getUserBalancesPaginated(db, page, pageSize)
	if err != nil {
		return nil, 0, err
	}

	// 转换为返回格式
	balances := rs.convertToUserBalanceInfo(users)
	return balances, total, nil
}

// getUserCount 获取用户总数
func (rs *RechargeService) getUserCount(db *gorm.DB) (int64, error) {
	var total int64
	err := db.Model(&model.User{}).Count(&total).Error
	if err != nil {
		return 0, fmt.Errorf("获取用户总数失败: %w", err)
	}
	return total, nil
}

// getUserBalancesPaginated 分页获取用户余额
func (rs *RechargeService) getUserBalancesPaginated(db *gorm.DB, page, pageSize int) ([]model.User, error) {
	var users []model.User
	offset := (page - 1) * pageSize

	err := db.Model(&model.User{}).
		Select("id, name, email, balance").
		Order("balance DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&users).Error
	if err != nil {
		return nil, fmt.Errorf("获取用户余额信息失败: %w", err)
	}

	return users, nil
}

// convertToUserBalanceInfo 转换为用户余额信息格式
func (rs *RechargeService) convertToUserBalanceInfo(users []model.User) []UserBalanceInfo {
	balances := make([]UserBalanceInfo, len(users))
	for i, user := range users {
		balances[i] = UserBalanceInfo{
			ID:      fmt.Sprintf("%d", user.ID),
			Name:    user.Name,
			Email:   user.Email,
			Balance: user.Balance,
			// LastRecharge 需要额外查询，这里暂时留空
		}
	}
	return balances
}
