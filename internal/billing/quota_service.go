package billing

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/settings"
	"git.uozi.org/uozi/potato-billing-api/types"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"
)

// QuotaService 资源包管理服务
type QuotaService struct {
	cacheService *CacheService
	config       *settings.BillingConfig
}

var (
	quotaServiceInstance *QuotaService
	quotaServiceOnce     sync.Once
)

// GetQuotaService 获取资源包管理服务单例
func GetQuotaService() *QuotaService {
	quotaServiceOnce.Do(func() {
		cacheService := GetCacheService()
		quotaServiceInstance = &QuotaService{
			cacheService: cacheService,
			config:       settings.Billing,
		}
		logger.Info("QuotaService singleton initialized")
	})
	return quotaServiceInstance
}

// TryQuotaBilling 尝试使用资源包计费
func (qs *QuotaService) TryQuotaBilling(tx *query.Query, userID uint64, apiKey, module, modelName string, usage int64) types.QuotaBillingResult {
	quotaPackages, err := qs.findAvailableQuotaPackages(tx, userID, apiKey, module, modelName)
	if err != nil || len(quotaPackages) == 0 {
		return types.QuotaBillingResult{Success: false, Message: "无可用资源包"}
	}

	// 尝试从资源包中扣减用量
	for _, pkg := range quotaPackages {
		if result := qs.tryDeductFromPackage(tx, pkg, usage); result.Success {
			return result
		}
	}

	return types.QuotaBillingResult{Success: false, Message: "资源包配额不足"}
}

// findAvailableQuotaPackages 查找可用的资源包
func (qs *QuotaService) findAvailableQuotaPackages(tx *query.Query, userID uint64, apiKey, module, modelName string) ([]model.QuotaPackageRecord, error) {
	var quotaPackages []model.QuotaPackageRecord
	err := tx.QuotaPackageRecord.UnderlyingDB().
		Where("user_id = ?", userID).
		Where("module = ?", module).
		Where("(model_name = ? OR model_name IS NULL OR model_name = '')", modelName).
		Where("status = ?", types.QuotaPackageStatusActive).
		Where("used < quota").
		Where("(api_key = ? OR api_key IS NULL OR api_key = '')", apiKey).
		Order(fmt.Sprintf("CASE WHEN model_name = '%s' THEN 0 ELSE 1 END", modelName)).
		Order(fmt.Sprintf("CASE WHEN api_key = '%s' THEN 0 ELSE 1 END", apiKey)).
		Order("CASE WHEN expires_at = 0 THEN 1 ELSE 0 END").
		Order("expires_at ASC").
		Find(&quotaPackages).
		Error
	return quotaPackages, err
}

// tryDeductFromPackage 尝试从资源包扣减用量
func (qs *QuotaService) tryDeductFromPackage(tx *query.Query, pkg model.QuotaPackageRecord, usage int64) types.QuotaBillingResult {
	if pkg.Available < usage {
		return types.QuotaBillingResult{Success: false}
	}

	// 扣减用量
	if err := qs.deductPackageUsage(tx, pkg.ID, usage); err != nil {
		return types.QuotaBillingResult{Success: false}
	}

	// 检查是否用完，更新状态
	if pkg.Available == usage {
		qs.updatePackageStatus(tx, pkg.ID, types.QuotaPackageStatusExhausted)
	}

	return types.QuotaBillingResult{
		Success:        true,
		QuotaPackageID: pkg.ID,
		Message:        "资源包计费成功",
	}
}

// deductPackageUsage 扣减资源包用量
func (qs *QuotaService) deductPackageUsage(tx *query.Query, packageID uint64, usage int64) error {
	_, err := tx.QuotaPackageRecord.
		Where(query.QuotaPackageRecord.ID.Eq(packageID)).
		Update(query.QuotaPackageRecord.Used, gorm.Expr("used + ?", usage))
	return err
}

// updatePackageStatus 更新资源包状态
func (qs *QuotaService) updatePackageStatus(tx *query.Query, packageID uint64, status string) error {
	_, err := tx.QuotaPackageRecord.
		Where(query.QuotaPackageRecord.ID.Eq(packageID)).
		Update(query.QuotaPackageRecord.Status, status)
	return err
}

// CreateQuotaPackage 创建资源包
func (qs *QuotaService) CreateQuotaPackage(ctx context.Context, pkg *model.QuotaPackageRecord) error {
	if err := qs.validateQuotaPackage(ctx, pkg); err != nil {
		return err
	}

	qs.setQuotaPackageDefaults(pkg)
	return cosy.UseDB(ctx).Create(pkg).Error
}

// validateQuotaPackage 验证资源包
func (qs *QuotaService) validateQuotaPackage(ctx context.Context, pkg *model.QuotaPackageRecord) error {
	db := cosy.UseDB(ctx)

	// 检查用户是否存在
	if !qs.userExists(db, pkg.UserID) {
		return fmt.Errorf("用户不存在")
	}

	// 如果指定了API Key，检查是否存在
	if pkg.AppID != 0 && !qs.appExists(db, pkg.AppID, pkg.UserID) {
		return fmt.Errorf("API Key不存在或不属于该用户")
	}

	return nil
}

// userExists 检查用户是否存在
func (qs *QuotaService) userExists(db *gorm.DB, userID uint64) bool {
	var exists bool
	err := db.Model(&model.User{}).
		Select("1").
		Where("id = ?", userID).
		First(&exists).Error
	return err == nil
}

// appExists 检查应用是否存在
func (qs *QuotaService) appExists(db *gorm.DB, appID, userID uint64) bool {
	var exists bool
	err := db.Model(&model.App{}).
		Select("1").
		Where("id = ? AND user_id = ?", appID, userID).
		First(&exists).Error
	return err == nil
}

// setQuotaPackageDefaults 设置资源包默认值
func (qs *QuotaService) setQuotaPackageDefaults(pkg *model.QuotaPackageRecord) {
	if pkg.Status == "" {
		pkg.Status = types.QuotaPackageStatusActive
	}
	if pkg.Type == "" {
		pkg.Type = types.QuotaPackageSourceAdmin
	}

	// 检查是否过期
	if pkg.ExpiresAt > 0 && pkg.ExpiresAt < time.Now().UnixMilli() {
		pkg.Status = types.QuotaPackageStatusExpired
	}
}

// UpdateQuotaPackage 更新资源包
func (qs *QuotaService) UpdateQuotaPackage(ctx context.Context, id uint64, updates map[string]interface{}) error {
	db := cosy.UseDB(ctx)

	// 获取原记录
	var pkg model.QuotaPackageRecord
	if err := db.First(&pkg, id).Error; err != nil {
		return err
	}

	// 更新记录
	err := db.Model(&pkg).Updates(updates).Error
	if err != nil {
		return err
	}

	// 清除相关缓存
	qs.invalidateQuotaCache(pkg.UserID, pkg.Module, pkg.AppID)
	return nil
}

// EnableQuotaPackage 启用资源包
func (qs *QuotaService) EnableQuotaPackage(ctx context.Context, id uint64) error {
	db := cosy.UseDB(ctx)

	// 获取资源包信息
	var pkg model.QuotaPackageRecord
	if err := db.First(&pkg, id).Error; err != nil {
		return err
	}

	// 检查状态
	status := qs.calculatePackageStatus(pkg)
	return qs.UpdateQuotaPackage(ctx, id, map[string]interface{}{"status": status})
}

// calculatePackageStatus 计算资源包状态
func (qs *QuotaService) calculatePackageStatus(pkg model.QuotaPackageRecord) string {
	if pkg.ExpiresAt > 0 && pkg.ExpiresAt < time.Now().UnixMilli() {
		return types.QuotaPackageStatusExpired
	}
	if pkg.Used >= pkg.Quota {
		return types.QuotaPackageStatusExhausted
	}
	return types.QuotaPackageStatusActive
}

// GetQuotaUsageStats 获取资源包使用统计
func (qs *QuotaService) GetQuotaUsageStats(ctx context.Context, userID uint64, module string) (map[string]interface{}, error) {
	db := cosy.UseDB(ctx)

	type QuotaStats struct {
		Status     string `json:"status"`
		Count      int64  `json:"count"`
		TotalQuota int64  `json:"total_quota"`
		TotalUsed  int64  `json:"total_used"`
		Available  int64  `json:"available"`
	}

	var stats []QuotaStats
	query := db.Model(&model.QuotaPackageRecord{}).
		Select("status, COUNT(*) as `count`, SUM(quota) as `total_quota`, SUM(used) as `total_used`, SUM(quota - used) as `available`").
		Where("user_id = ?", userID).
		Group("status")

	if module != "" {
		query = query.Where("module = ?", module)
	}

	if err := query.Find(&stats).Error; err != nil {
		return nil, err
	}

	// 转换为map格式返回
	result := make(map[string]interface{})
	for _, stat := range stats {
		result[stat.Status] = stat
	}

	return result, nil
}

// CreateQuotaPackageFromRequest 从请求创建配额包
func (qs *QuotaService) CreateQuotaPackageFromRequest(ctx context.Context, req *CreateQuotaPackageRequest, operatorID uint64) (*model.QuotaPackageRecord, error) {
	quotaPackage := qs.buildQuotaPackageFromRequest(req, operatorID)

	// 使用现有的创建方法
	err := qs.CreateQuotaPackage(ctx, quotaPackage)
	if err != nil {
		return nil, fmt.Errorf("创建配额包失败: %w", err)
	}

	return quotaPackage, nil
}

// buildQuotaPackageFromRequest 从请求构建配额包
func (qs *QuotaService) buildQuotaPackageFromRequest(req *CreateQuotaPackageRequest, operatorID uint64) *model.QuotaPackageRecord {
	quotaPackage := &model.QuotaPackageRecord{
		UserID:      req.UserID,
		AppID:       req.AppID,
		Module:      req.Module,
		ModelName:   req.ModelName,
		Quota:       req.Quota,
		Used:        0,
		ExpiresAt:   req.ExpiresAt,
		Status:      types.QuotaPackageStatusActive,
		Type:        req.Type,
		Description: req.Description,
		OperatorID:  operatorID,
	}

	if req.Type == "" {
		quotaPackage.Type = types.QuotaPackageSourceAdmin
	}

	return quotaPackage
}

// invalidateQuotaCache 清除配额缓存
func (qs *QuotaService) invalidateQuotaCache(userID uint64, module string, appID uint64) {
	logger.Debug("清除配额缓存", "user_id", userID, "module", module, "app_id", appID)
}

// CreateQuotaPackageRequest 创建配额包请求结构
type CreateQuotaPackageRequest struct {
	UserID      uint64 `json:"user_id,string" binding:"required"`
	AppID       uint64 `json:"app_id,string,omitempty"`
	Module      string `json:"module" binding:"required"`
	ModelName   string `json:"model_name,omitempty"`
	Quota       int64  `json:"quota" binding:"required,min=1"`
	ExpiresAt   int64  `json:"expires_at,omitempty"`
	Type        string `json:"type,omitempty"`
	Description string `json:"description,omitempty"`
}

// UpdateQuotaPackageRequest 更新配额包请求结构
type UpdateQuotaPackageRequest struct {
	Quota       *int64  `json:"quota,omitempty"`
	ExpiresAt   *int64  `json:"expires_at,omitempty"`
	Description *string `json:"description,omitempty"`
}

// QuotaPackageListQuery 配额包列表查询参数
type QuotaPackageListQuery struct {
	UserID   uint64 `form:"user_id"`
	AppID    uint64 `form:"app_id"`
	Module   string `form:"module"`
	Status   string `form:"status"`
	Page     int    `form:"page"`
	PageSize int    `form:"pageSize"`
}

// CreateQuotaPackageFromRequest 从请求创建配额包
func (qs *QuotaService) CreateQuotaPackageFromRequest(ctx context.Context, req *CreateQuotaPackageRequest, operatorID uint64) (*model.QuotaPackageRecord, error) {
	// 创建配额包记录
	quotaPackage := &model.QuotaPackageRecord{
		UserID:      req.UserID,
		AppID:       req.AppID,
		Module:      req.Module,
		ModelName:   req.ModelName,
		Quota:       req.Quota,
		Used:        0,
		ExpiresAt:   req.ExpiresAt,
		Status:      types.QuotaPackageStatusActive,
		Type:        req.Type,
		Description: req.Description,
		OperatorID:  operatorID,
	}

	if req.Type == "" {
		quotaPackage.Type = types.QuotaPackageSourceAdmin
	}

	// 使用现有的创建方法
	err := qs.CreateQuotaPackage(ctx, quotaPackage)
	if err != nil {
		return nil, fmt.Errorf("创建配额包失败: %w", err)
	}

	return quotaPackage, nil
}

// UpdateQuotaPackageFromRequest 从请求更新配额包
func (qs *QuotaService) UpdateQuotaPackageFromRequest(ctx context.Context, id uint64, req *UpdateQuotaPackageRequest) error {
	// 构建更新数据
	updates := make(map[string]interface{})
	if req.Quota != nil {
		updates["quota"] = *req.Quota
	}
	if req.ExpiresAt != nil {
		updates["expires_at"] = *req.ExpiresAt
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}

	if len(updates) == 0 {
		return fmt.Errorf("没有需要更新的字段")
	}

	// 使用现有的更新方法
	return qs.UpdateQuotaPackage(ctx, id, updates)
}
