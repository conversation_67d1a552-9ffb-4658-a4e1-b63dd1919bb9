package billing

import (
	"errors"
	"fmt"
	"sync"

	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/settings"
	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"
)

// PricingService 价格规则服务
type PricingService struct {
	cacheService *CacheService
	config       *settings.BillingConfig
}

var (
	pricingServiceInstance *PricingService
	pricingServiceOnce     sync.Once
)

// GetPricingService 获取价格规则服务单例
func GetPricingService() *PricingService {
	pricingServiceOnce.Do(func() {
		cacheService := GetCacheService()
		pricingServiceInstance = &PricingService{
			cacheService: cacheService,
			config:       settings.Billing,
		}
		logger.Info("PricingService singleton initialized")
	})
	return pricingServiceInstance
}

// FindEffectivePricingRule 查找生效的价格规则
func (ps *PricingService) FindEffectivePricingRule(module, modelName string) (*model.PricingRule, error) {
	// 先尝试从缓存获取
	if cachedRule, err := ps.cacheService.GetPricingRules(module, modelName); err == nil && cachedRule != nil {
		return cachedRule, nil
	}

	// 查找具体模型的规则
	if modelName != "" {
		if rule, err := ps.findSpecificModelRule(module, modelName); err == nil {
			ps.cachePricingRule(rule)
			return rule, nil
		}
	}

	// 查找通用规则
	rule, err := ps.findGenericRule(module)
	if err != nil {
		return nil, fmt.Errorf("未找到模块 %s 的价格规则: %w", module, err)
	}

	ps.cachePricingRule(rule)
	return rule, nil
}

// findSpecificModelRule 查找特定模型的价格规则
func (ps *PricingService) findSpecificModelRule(module, modelName string) (*model.PricingRule, error) {
	return query.PricingRule.
		Where(
			query.PricingRule.Module.Eq(module),
			query.PricingRule.ModelName.Eq(modelName),
			query.PricingRule.IsActive.Is(true),
		).
		Order(query.PricingRule.Priority.Desc()).
		First()
}

// findGenericRule 查找通用价格规则
func (ps *PricingService) findGenericRule(module string) (*model.PricingRule, error) {
	return query.PricingRule.
		Where(
			query.PricingRule.Module.Eq(module),
			query.PricingRule.ModelName.Eq(""),
			query.PricingRule.IsActive.Is(true),
		).
		Order(query.PricingRule.Priority.Desc()).
		First()
}

// cachePricingRule 缓存价格规则
func (ps *PricingService) cachePricingRule(rule *model.PricingRule) {
	if err := ps.cacheService.SetPricingRules(rule, ps.config.CacheTTL); err != nil {
		logger.Error("缓存价格规则失败", "error", err)
	}
}

// PreloadPricingRules 预加载价格规则到缓存
func (ps *PricingService) PreloadPricingRules() {
	logger.Info("开始预加载价格规则到缓存")

	rules, err := ps.getAllActiveRules()
	if err != nil {
		logger.Error("查询价格规则失败", "error", err)
		return
	}

	successCount := ps.cacheAllRules(rules)
	logger.Info("价格规则预加载完成", "total", len(rules), "success", successCount)
}

// getAllActiveRules 获取所有激活的价格规则
func (ps *PricingService) getAllActiveRules() ([]*model.PricingRule, error) {
	return query.PricingRule.
		Where(query.PricingRule.IsActive.Is(true)).
		Find()
}

// cacheAllRules 缓存所有规则
func (ps *PricingService) cacheAllRules(rules []*model.PricingRule) int {
	successCount := 0
	for _, rule := range rules {
		if err := ps.cacheService.SetPricingRules(rule, ps.config.CacheTTL); err != nil {
			logger.Error("缓存价格规则失败", "error", err, "rule_id", rule.ID)
		} else {
			successCount++
		}
	}
	return successCount
}

// GetActivePricingRules 获取所有激活的价格规则
func (ps *PricingService) GetActivePricingRules() ([]*model.PricingRule, error) {
	rules, err := query.PricingRule.
		Where(query.PricingRule.IsActive.Is(true)).
		Order(query.PricingRule.Module.Asc(), query.PricingRule.Priority.Desc()).
		Find()
	return rules, err
}

// CreatePricingRule 创建价格规则
func (ps *PricingService) CreatePricingRule(rule *model.PricingRule) error {
	// 检查是否已存在相同的规则
	if ps.ruleExists(rule.Module, rule.ModelName) {
		return fmt.Errorf("已存在相同的价格规则")
	}

	// 创建新规则
	if err := query.PricingRule.Create(rule); err != nil {
		return err
	}

	// 清除相关缓存
	ps.invalidatePricingRuleCache(rule.Module, rule.ModelName)
	return nil
}

// ruleExists 检查规则是否存在
func (ps *PricingService) ruleExists(module, modelName string) bool {
	_, err := query.PricingRule.
		Where(
			query.PricingRule.Module.Eq(module),
			query.PricingRule.ModelName.Eq(modelName),
			query.PricingRule.IsActive.Is(true),
		).
		First()
	return err == nil
}

// UpdatePricingRule 更新价格规则
func (ps *PricingService) UpdatePricingRule(ruleID uint64, updates map[string]interface{}) error {

	// 先获取原规则
	rule, err := query.PricingRule.
		Where(query.PricingRule.ID.Eq(ruleID)).
		First()
	if err != nil {
		return err
	}

	// 更新规则
	if _, err := query.PricingRule.Where(query.PricingRule.ID.Eq(ruleID)).Updates(updates); err != nil {
		return err
	}

	// 清除相关缓存
	ps.invalidatePricingRuleCache(rule.Module, rule.ModelName)

	return nil
}

// DeletePricingRule 删除价格规则（软删除）
func (ps *PricingService) DeletePricingRule(ruleID uint64) error {

	// 先获取规则信息
	rule, err := query.PricingRule.
		Where(query.PricingRule.ID.Eq(ruleID)).
		First()
	if err != nil {
		return err
	}

	// 软删除
	if _, err := query.PricingRule.Where(query.PricingRule.ID.Eq(ruleID)).Delete(); err != nil {
		return err
	}

	// 清除相关缓存
	ps.invalidatePricingRuleCache(rule.Module, rule.ModelName)

	return nil
}

// invalidatePricingRuleCache 清除价格规则缓存
func (ps *PricingService) invalidatePricingRuleCache(module, modelName string) {
	// 清除具体模型的缓存
	if modelName != "" {
		if err := ps.cacheService.InvalidatePricingRules(module, modelName); err != nil {
			logger.Error("清除价格规则缓存失败", "error: ", err, "module: ", module, "model: ", modelName)
		}
	}

	// 清除通用规则的缓存
	if err := ps.cacheService.InvalidatePricingRules(module, ""); err != nil {
		logger.Error("清除通用价格规则缓存失败", "error: ", err, "module: ", module)
	}
}

// PricingRuleListQuery 定价规则列表查询参数
type PricingRuleListQuery struct {
	Module   string `form:"module"`
	IsActive *bool  `form:"is_active"`
	Page     int    `form:"page"`
	PageSize int    `form:"pageSize"`
}

// PricingRuleListResponse 定价规则列表响应
type PricingRuleListResponse struct {
	Data       []*model.PricingRule   `json:"data"`
	Pagination map[string]interface{} `json:"pagination"`
}

// GetPricingRuleList 获取定价规则列表
func (ps *PricingService) GetPricingRuleList(queryParams *PricingRuleListQuery) (*PricingRuleListResponse, error) {
	q := query.PricingRule

	queryBuilder := q.Select(q.ALL)

	// 添加模块过滤
	if queryParams.Module != "" {
		queryBuilder = queryBuilder.Where(q.Module.Eq(queryParams.Module))
	}

	// 添加激活状态过滤
	if queryParams.IsActive != nil {
		queryBuilder = queryBuilder.Where(q.IsActive.Is(*queryParams.IsActive))
	}

	// 获取总数
	total, err := queryBuilder.Count()
	if err != nil {
		return nil, fmt.Errorf("获取定价规则总数失败: %w", err)
	}

	// 设置分页参数
	if queryParams.Page < 1 {
		queryParams.Page = 1
	}
	if queryParams.PageSize < 1 || queryParams.PageSize > 100 {
		queryParams.PageSize = 20
	}

	// 分页查询
	offset := (queryParams.Page - 1) * queryParams.PageSize
	rules, err := queryBuilder.
		Order(q.Module.Asc(), q.Priority.Desc(), q.CreatedAt.Desc()).
		Limit(queryParams.PageSize).
		Offset(offset).
		Find()
	if err != nil {
		return nil, fmt.Errorf("获取定价规则列表失败: %w", err)
	}

	// 构建响应
	response := &PricingRuleListResponse{
		Data: rules,
		Pagination: map[string]interface{}{
			"total":        total,
			"per_page":     queryParams.PageSize,
			"current_page": queryParams.Page,
			"total_pages":  (total + int64(queryParams.PageSize) - 1) / int64(queryParams.PageSize),
		},
	}

	return response, nil
}

// GetPricingRule 获取单个定价规则
func (ps *PricingService) GetPricingRule(id uint64) (*model.PricingRule, error) {
	rule, err := query.PricingRule.Where(query.PricingRule.ID.Eq(id)).First()
	if err != nil {
		return nil, fmt.Errorf("获取定价规则失败: %w", err)
	}
	return rule, nil
}
