package valid

import (
	"fmt"
	"time"

	"github.com/uozi-tech/cosy/logger"
	"github.com/uozi-tech/cosy/redis"
)

const (
	VerifyKey   = "valid:verify:%s"
	ThrottleKey = "valid:throttle:%s"
)

func CheckExist(key string) any {
	value, err := redis.Get(key)
	if err != nil {
		logger.Debug(err)
		return nil
	}
	return value
}

func Del(key string) {
	_ = redis.Del(key)
}

func SetWithTTL(key string, value interface{}, ttl time.Duration) {
	_ = redis.SetEx(key, value, ttl)
}

func CheckCode(AreaCode int, Phone string, VerifyCode string) bool {
	phone := fmt.Sprintf("%d%s", AreaCode, Phone)
	verifyPhone := fmt.Sprintf(VerifyKey, phone)
	throttlePhone := fmt.Sprintf(ThrottleKey, phone)

	if verifyCode := CheckExist(verifyPhone); verifyCode == nil || verifyCode.(string) != VerifyCode {
		logger.Debugf("verification code for %s is error: %s, expected: %s", phone, VerifyCode, verifyCode)
		return false
	}

	Del(verifyPhone)
	Del(throttlePhone)
	return true
}
